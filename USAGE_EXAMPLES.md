# Date Filtering Usage Examples

## Quick Start

The transaction history can now be filtered by date range using simple `startDate` and `endDate` parameters.

## Basic Examples

### 1. Get transactions from a specific date onwards
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01
```

### 2. Get transactions up to a specific date
```bash
GET /api/v1/transaction/my-transactions?endDate=2024-01-31
```

### 3. Get transactions within a date range
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31
```

## Advanced Examples

### 4. Filter by date range and transaction type
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31&type=TRANSFER
```

### 5. Filter by date range, type, and status
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31&type=CASH_OUT&status=COMPLETED
```

### 6. Date range with pagination
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31&page=1&limit=20
```

### 7. Date range with sorting
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31&sort=-amount
```

### 8. Date range with specific fields
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31&fields=transactionId,type,amount,createdAt
```

## Common Use Cases

### Today's Transactions
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-15&endDate=2024-01-15
```

### This Week's Transactions
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-08&endDate=2024-01-14
```

### This Month's Transactions
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31
```

### Last 30 Days
```bash
GET /api/v1/transaction/my-transactions?startDate=2023-12-15&endDate=2024-01-14
```

### Quarterly Report
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-03-31&limit=1000
```

## JavaScript/Frontend Examples

### Using Fetch API
```javascript
// Get transactions for January 2024
const response = await fetch('/api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const data = await response.json();
```

### Using Axios
```javascript
// Get completed transfers in January 2024
const response = await axios.get('/api/v1/transaction/my-transactions', {
  params: {
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    type: 'TRANSFER',
    status: 'COMPLETED'
  },
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### Dynamic Date Range
```javascript
// Get transactions for the last 7 days
const endDate = new Date();
const startDate = new Date();
startDate.setDate(startDate.getDate() - 7);

const response = await fetch(`/api/v1/transaction/my-transactions?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Response Format

All responses maintain the same format:

```json
{
  "success": true,
  "message": "My transactions retrieved successfully!",
  "data": [
    {
      "transactionId": "TXN1234567890",
      "type": "TRANSFER",
      "amount": 1000,
      "status": "COMPLETED",
      "createdAt": "2024-01-15T10:30:00.000Z",
      // ... other transaction fields
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPage": 3
  }
}
```

## Error Handling

### Invalid Date Format
```bash
GET /api/v1/transaction/my-transactions?startDate=invalid-date
# Returns: 400 Bad Request with appropriate error message
```

### Start Date After End Date
The API will still work but may return no results if the date range is invalid.

## Performance Tips

1. **Use specific date ranges**: Avoid very large date ranges for better performance
2. **Combine with pagination**: Use `limit` parameter for large result sets
3. **Use field selection**: Use `fields` parameter to get only needed data
4. **Add other filters**: Combine with `type`, `status` etc. to reduce result set

## Migration from Old Format

### Old way (still supported):
```bash
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01T00:00:00.000Z&createdAt[lte]=2024-01-31T23:59:59.999Z
```

### New way (recommended):
```bash
GET /api/v1/transaction/my-transactions?startDate=2024-01-01&endDate=2024-01-31
```

Both approaches work, but the new way is more user-friendly and handles time zones automatically.
