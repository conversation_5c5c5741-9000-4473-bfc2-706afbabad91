# Get My Transactions API - All Possible URLs with Query Strings

## Base URL

```
GET /api/v1/transaction/my-transactions
```

## Quick Reference Table

| Parameter       | Type   | Description                           | Example Values                                                       |
| --------------- | ------ | ------------------------------------- | -------------------------------------------------------------------- |
| `searchTerm`    | String | Search across searchable fields       | `TXN123`, `TRANSFER`, `salary`                                       |
| `type`          | Enum   | Filter by transaction type            | `TRANSFER`, `CASH_IN`, `CASH_OUT`, `DEPOSIT`, `WITHDRAWAL`, `REFUND` |
| `status`        | Enum   | Filter by transaction status          | `PENDING`, `COMPLETED`, `FAILED`, `CANCELLED`, `REFUNDED`            |
| `amount`        | Number | Filter by amount (supports operators) | `100`, `amount[gte]=100`, `amount[lte]=1000`                         |
| `fee`           | Number | Filter by fee amount                  | `0`, `fee[gt]=0`                                                     |
| `commission`    | Number | Filter by commission                  | `commission[gt]=0`                                                   |
| `reference`     | String | Filter by reference                   | `REF123`, `SALARY-JAN-2024`                                          |
| `description`   | String | Filter by description                 | `Monthly salary`                                                     |
| `transactionId` | String | Filter by transaction ID              | `TXN1234567890`                                                      |
| `createdAt`     | Date   | Filter by creation date               | `2024-01-01`, `createdAt[gte]=2024-01-01`                            |
| `sort`          | String | Sort results                          | `-createdAt`, `amount`, `type,-amount`                               |
| `fields`        | String | Select specific fields                | `transactionId,type,amount`, `-balanceBefore`                        |
| `page`          | Number | Page number                           | `1`, `2`, `10`                                                       |
| `limit`         | Number | Results per page                      | `10`, `20`, `50`, `100`                                              |

## Query Parameters Overview

### Core Query Parameters

- **searchTerm**: Search across searchable fields
- **sort**: Sort results by field(s)
- **fields**: Select specific fields to return
- **page**: Page number for pagination
- **limit**: Number of results per page

### Filter Parameters (Direct MongoDB field filtering)

- **type**: Filter by transaction type
- **status**: Filter by transaction status
- **amount**: Filter by amount (exact match or range)
- **fee**: Filter by fee amount
- **commission**: Filter by commission amount
- **reference**: Filter by reference
- **description**: Filter by description
- **transactionId**: Filter by transaction ID
- **sender**: Filter by sender user ID
- **receiver**: Filter by receiver user ID
- **agent**: Filter by agent user ID
- **senderWallet**: Filter by sender wallet ID
- **receiverWallet**: Filter by receiver wallet ID
- **agentWallet**: Filter by agent wallet ID
- **createdAt**: Filter by creation date
- **updatedAt**: Filter by update date

## 1. Basic URLs

### Default (no parameters)

```
GET /api/v1/transaction/my-transactions
```

### With Authentication Header

```
GET /api/v1/transaction/my-transactions
Authorization: Bearer <access_token>
```

## 2. Search URLs

### Search by transaction ID

```
GET /api/v1/transaction/my-transactions?searchTerm=TXN1234567890
```

### Search by transaction type

```
GET /api/v1/transaction/my-transactions?searchTerm=TRANSFER
GET /api/v1/transaction/my-transactions?searchTerm=CASH_IN
GET /api/v1/transaction/my-transactions?searchTerm=CASH_OUT
GET /api/v1/transaction/my-transactions?searchTerm=DEPOSIT
GET /api/v1/transaction/my-transactions?searchTerm=WITHDRAWAL
GET /api/v1/transaction/my-transactions?searchTerm=REFUND
```

### Search by status

```
GET /api/v1/transaction/my-transactions?searchTerm=PENDING
GET /api/v1/transaction/my-transactions?searchTerm=COMPLETED
GET /api/v1/transaction/my-transactions?searchTerm=FAILED
GET /api/v1/transaction/my-transactions?searchTerm=CANCELLED
GET /api/v1/transaction/my-transactions?searchTerm=REFUNDED
```

### Search by reference

```
GET /api/v1/transaction/my-transactions?searchTerm=REF123
GET /api/v1/transaction/my-transactions?searchTerm=REFUND-TXN
```

### Search by description

```
GET /api/v1/transaction/my-transactions?searchTerm=salary
GET /api/v1/transaction/my-transactions?searchTerm=payment
```

## 3. Filter URLs

### Filter by Transaction Type

```
GET /api/v1/transaction/my-transactions?type=TRANSFER
GET /api/v1/transaction/my-transactions?type=CASH_IN
GET /api/v1/transaction/my-transactions?type=CASH_OUT
GET /api/v1/transaction/my-transactions?type=DEPOSIT
GET /api/v1/transaction/my-transactions?type=WITHDRAWAL
GET /api/v1/transaction/my-transactions?type=REFUND
```

### Filter by Status

```
GET /api/v1/transaction/my-transactions?status=PENDING
GET /api/v1/transaction/my-transactions?status=COMPLETED
GET /api/v1/transaction/my-transactions?status=FAILED
GET /api/v1/transaction/my-transactions?status=CANCELLED
GET /api/v1/transaction/my-transactions?status=REFUNDED
```

### Filter by Amount

```
GET /api/v1/transaction/my-transactions?amount=100
GET /api/v1/transaction/my-transactions?amount[gte]=100
GET /api/v1/transaction/my-transactions?amount[lte]=1000
GET /api/v1/transaction/my-transactions?amount[gte]=100&amount[lte]=1000
```

### Filter by Fee

```
GET /api/v1/transaction/my-transactions?fee=0
GET /api/v1/transaction/my-transactions?fee[gt]=0
GET /api/v1/transaction/my-transactions?fee[gte]=5&fee[lte]=50
```

### Filter by Commission

```
GET /api/v1/transaction/my-transactions?commission=0
GET /api/v1/transaction/my-transactions?commission[gt]=0
```

### Filter by Reference

```
GET /api/v1/transaction/my-transactions?reference=REF123
GET /api/v1/transaction/my-transactions?reference=SALARY-JAN-2024
```

### Filter by Description

```
GET /api/v1/transaction/my-transactions?description=Monthly%20salary
```

### Filter by Transaction ID

```
GET /api/v1/transaction/my-transactions?transactionId=TXN1234567890
```

### Filter by Date Range

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01
GET /api/v1/transaction/my-transactions?createdAt[lte]=2024-12-31
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01&createdAt[lte]=2024-01-31
```

## 4. Sorting URLs

### Sort by Creation Date (Default: newest first)

```
GET /api/v1/transaction/my-transactions?sort=-createdAt
GET /api/v1/transaction/my-transactions?sort=createdAt
```

### Sort by Amount

```
GET /api/v1/transaction/my-transactions?sort=amount
GET /api/v1/transaction/my-transactions?sort=-amount
```

### Sort by Transaction Type

```
GET /api/v1/transaction/my-transactions?sort=type
GET /api/v1/transaction/my-transactions?sort=-type
```

### Sort by Status

```
GET /api/v1/transaction/my-transactions?sort=status
GET /api/v1/transaction/my-transactions?sort=-status
```

### Multiple Sort Fields

```
GET /api/v1/transaction/my-transactions?sort=-createdAt,amount
GET /api/v1/transaction/my-transactions?sort=type,-amount
GET /api/v1/transaction/my-transactions?sort=status,-createdAt,amount
```

## 5. Field Selection URLs

### Select Specific Fields

```
GET /api/v1/transaction/my-transactions?fields=transactionId,type,amount,status
GET /api/v1/transaction/my-transactions?fields=transactionId,amount,fee,createdAt
GET /api/v1/transaction/my-transactions?fields=type,status,reference,description
```

### Exclude Sensitive Fields

```
GET /api/v1/transaction/my-transactions?fields=-senderBalanceBefore,-senderBalanceAfter,-receiverBalanceBefore,-receiverBalanceAfter
```

## 6. Pagination URLs

### Basic Pagination

```
GET /api/v1/transaction/my-transactions?page=1&limit=10
GET /api/v1/transaction/my-transactions?page=2&limit=20
GET /api/v1/transaction/my-transactions?page=1&limit=50
```

### Different Page Sizes

```
GET /api/v1/transaction/my-transactions?limit=5
GET /api/v1/transaction/my-transactions?limit=25
GET /api/v1/transaction/my-transactions?limit=100
```

### Navigate Pages

```
GET /api/v1/transaction/my-transactions?page=1
GET /api/v1/transaction/my-transactions?page=2
GET /api/v1/transaction/my-transactions?page=10
```

## 7. Complex Combined URLs

### Filter + Search + Sort + Pagination

```
GET /api/v1/transaction/my-transactions?type=TRANSFER&status=COMPLETED&searchTerm=salary&sort=-createdAt&page=1&limit=20
GET /api/v1/transaction/my-transactions?amount[gte]=100&amount[lte]=1000&sort=amount&page=1&limit=10
GET /api/v1/transaction/my-transactions?status=COMPLETED&createdAt[gte]=2024-01-01&sort=-amount&fields=transactionId,type,amount,createdAt
```

### Date Range + Type + Status

```
GET /api/v1/transaction/my-transactions?type=CASH_OUT&status=COMPLETED&createdAt[gte]=2024-01-01&createdAt[lte]=2024-01-31
GET /api/v1/transaction/my-transactions?type=DEPOSIT&createdAt[gte]=2024-01-01&sort=-createdAt&limit=50
```

### Amount Range + Fee Filter

```
GET /api/v1/transaction/my-transactions?amount[gte]=500&fee[gt]=0&sort=-amount&fields=transactionId,amount,fee,type
GET /api/v1/transaction/my-transactions?amount[lte]=100&fee=0&type=TRANSFER&sort=-createdAt
```

### Search + Multiple Filters

```
GET /api/v1/transaction/my-transactions?searchTerm=REF&type=TRANSFER&status=COMPLETED&sort=-createdAt&page=1&limit=15
GET /api/v1/transaction/my-transactions?searchTerm=payment&amount[gte]=1000&sort=amount&fields=transactionId,type,amount,description
```

## 8. Agent-Specific URLs (for users with AGENT role)

### Agent Commission Transactions

```
GET /api/v1/transaction/my-transactions?commission[gt]=0&sort=-commission
GET /api/v1/transaction/my-transactions?type=CASH_IN&commission[gt]=0&sort=-createdAt
GET /api/v1/transaction/my-transactions?type=CASH_OUT&commission[gt]=0&sort=-createdAt
```

### Agent Cash-In/Cash-Out

```
GET /api/v1/transaction/my-transactions?type=CASH_IN&sort=-createdAt
GET /api/v1/transaction/my-transactions?type=CASH_OUT&sort=-createdAt
GET /api/v1/transaction/my-transactions?type=CASH_IN,CASH_OUT&sort=-createdAt
```

## 9. Date-Based URLs

### Today's Transactions

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-15T00:00:00.000Z&createdAt[lte]=2024-01-15T23:59:59.999Z
```

### This Week's Transactions

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-08&createdAt[lte]=2024-01-14
```

### This Month's Transactions

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01&createdAt[lte]=2024-01-31
```

### Last 30 Days

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2023-12-15&sort=-createdAt
```

### Specific Date Range with Time

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01T00:00:00.000Z&createdAt[lte]=2024-01-31T23:59:59.999Z
```

## 10. Performance Optimized URLs

### Minimal Fields for List View

```
GET /api/v1/transaction/my-transactions?fields=transactionId,type,amount,status,createdAt&limit=20&sort=-createdAt
```

### Summary View

```
GET /api/v1/transaction/my-transactions?fields=type,amount,status&limit=100&sort=-createdAt
```

### Quick Status Check

```
GET /api/v1/transaction/my-transactions?status=PENDING&fields=transactionId,type,amount,createdAt&sort=-createdAt
```

## 11. Error Handling URLs

### Invalid Parameters (will be ignored or cause errors)

```
GET /api/v1/transaction/my-transactions?invalidParam=test
GET /api/v1/transaction/my-transactions?type=INVALID_TYPE
GET /api/v1/transaction/my-transactions?status=INVALID_STATUS
GET /api/v1/transaction/my-transactions?amount=invalid
GET /api/v1/transaction/my-transactions?page=0
GET /api/v1/transaction/my-transactions?limit=0
```

## 12. Special Use Cases

### Export All Transactions

```
GET /api/v1/transaction/my-transactions?limit=10000&sort=-createdAt
```

### Failed Transactions Only

```
GET /api/v1/transaction/my-transactions?status=FAILED&sort=-createdAt&fields=transactionId,type,amount,description,createdAt
```

### High-Value Transactions

```
GET /api/v1/transaction/my-transactions?amount[gte]=10000&sort=-amount&fields=transactionId,type,amount,status,createdAt
```

### Refunded Transactions

```
GET /api/v1/transaction/my-transactions?status=REFUNDED&sort=-createdAt&fields=transactionId,type,amount,reference,description
```

### Zero-Fee Transactions

```
GET /api/v1/transaction/my-transactions?fee=0&sort=-createdAt&fields=transactionId,type,amount,fee
```

## 13. URL Encoding Examples

### Special Characters in Search

```
GET /api/v1/transaction/my-transactions?searchTerm=Monthly%20Salary%20Payment
GET /api/v1/transaction/my-transactions?description=Payment%20for%20services%20%26%20goods
GET /api/v1/transaction/my-transactions?reference=REF%2D2024%2D001
```

### Date with Special Characters

```
GET /api/v1/transaction/my-transactions?createdAt[gte]=2024-01-01T00%3A00%3A00.000Z
```

## Notes:

1. All URLs require authentication via Bearer token in Authorization header
2. MongoDB operators like `[gte]`, `[lte]`, `[gt]`, `[lt]`, `[ne]` can be used for numeric and date fields
3. The `searchTerm` parameter searches across: transactionId, type, status, reference, description
4. Default sorting is by `-createdAt` (newest first)
5. Default pagination is page=1, limit=10
6. Field selection with `fields` parameter supports both inclusion and exclusion (prefix with -)
7. Multiple values for the same parameter can be comma-separated in some cases
